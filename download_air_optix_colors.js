// Script to download Air Optix Colors product data from diopta.rs WooCommerce API
const fs = require('fs');
const WooCommerceRestApi = require('@woocommerce/woocommerce-rest-api').default;

// WooCommerce API credentials (from the existing script)
const api = new WooCommerceRestApi({
  url: 'https://diopta.rs',
  consumerKey: 'ck_7af73fa54215777a13b8bd85d680024eb29b4ae5',
  consumerSecret: 'cs_4642f139b6cd4857a1b1c0099b94a131a39bf376',
  version: 'wc/v3'
});

// Output file
const OUTPUT_FILE = 'air_optix_colors_data.csv';

// Function to search for Air Optix Colors products
async function downloadAirOptixColors() {
  console.log('Searching for Air Optix Colors products...');

  try {
    // Search for products containing "air optix color"
    const searchResponse = await api.get('products', {
      search: 'air optix color',
      per_page: 100,
      status: 'publish'
    });

    console.log(`Found ${searchResponse.data.length} products matching "air optix color"`);

    if (searchResponse.data.length === 0) {
      console.log('No Air Optix Colors products found. Trying alternative search...');
      
      // Try alternative search terms
      const altSearchResponse = await api.get('products', {
        search: 'air optix colors',
        per_page: 100,
        status: 'publish'
      });
      
      console.log(`Found ${altSearchResponse.data.length} products matching "air optix colors"`);
      
      if (altSearchResponse.data.length === 0) {
        console.log('No products found. Exiting...');
        return;
      }
      
      searchResponse.data = altSearchResponse.data;
    }

    // Prepare CSV header
    const csvHeader = [
      'Type',
      'SKU',
      'Name',
      'Published',
      'Is featured?',
      'Short description',
      'Description',
      'Regular price',
      'Sale price',
      'Categories',
      'Tags',
      'Images',
      'Stock',
      'In stock?',
      'Weight (unit)',
      'Length (unit)',
      'Width (unit)',
      'Height (unit)',
      'Shipping class',
      'Tax status',
      'Tax class',
      'Attribute 1 name',
      'Attribute 1 value(s)',
      'Attribute 1 visible',
      'Attribute 1 global',
      'Attribute 2 name',
      'Attribute 2 value(s)',
      'Attribute 2 visible',
      'Attribute 2 global',
      'Attribute 3 name',
      'Attribute 3 value(s)',
      'Attribute 3 visible',
      'Attribute 3 global',
      'Attribute 4 name',
      'Attribute 4 value(s)',
      'Attribute 4 visible',
      'Attribute 4 global',
      'Attribute 5 name',
      'Attribute 5 value(s)',
      'Attribute 5 visible',
      'Attribute 5 global',
      'Parent'
    ];

    // Write CSV header
    fs.writeFileSync(OUTPUT_FILE, csvHeader.join(',') + '\n');

    // Process each product
    for (const product of searchResponse.data) {
      console.log(`Processing product: ${product.name} (ID: ${product.id})`);
      
      // Get full product details
      const fullProduct = await api.get(`products/${product.id}`);
      const productData = fullProduct.data;
      
      // Extract categories
      const categories = productData.categories.map(cat => cat.name).join(';');
      
      // Extract tags
      const tags = productData.tags.map(tag => tag.name).join(';');
      
      // Extract images
      const images = productData.images.map(img => img.src).join(';');
      
      // Extract attributes
      const attributes = productData.attributes || [];
      const attributeData = {};
      
      for (let i = 0; i < 5; i++) {
        const attr = attributes[i];
        if (attr) {
          attributeData[`attr${i+1}_name`] = attr.name;
          attributeData[`attr${i+1}_values`] = attr.options.join(',');
          attributeData[`attr${i+1}_visible`] = attr.visible ? 1 : 0;
          attributeData[`attr${i+1}_global`] = attr.variation ? 0 : 1;
        } else {
          attributeData[`attr${i+1}_name`] = '';
          attributeData[`attr${i+1}_values`] = '';
          attributeData[`attr${i+1}_visible`] = '';
          attributeData[`attr${i+1}_global`] = '';
        }
      }
      
      // Create parent product row
      const parentRow = [
        productData.type, // Type
        productData.sku || '', // SKU
        `"${productData.name.replace(/"/g, '""')}"`, // Name (escaped)
        productData.status === 'publish' ? 1 : 0, // Published
        productData.featured ? 1 : 0, // Is featured
        `"${(productData.short_description || '').replace(/"/g, '""')}"`, // Short description
        `"${(productData.description || '').replace(/"/g, '""')}"`, // Description
        productData.regular_price || '', // Regular price
        productData.sale_price || '', // Sale price
        `"${categories}"`, // Categories
        `"${tags}"`, // Tags
        `"${images}"`, // Images
        productData.stock_quantity || '', // Stock
        productData.in_stock ? 1 : 0, // In stock
        productData.weight || '', // Weight
        productData.dimensions.length || '', // Length
        productData.dimensions.width || '', // Width
        productData.dimensions.height || '', // Height
        productData.shipping_class || '', // Shipping class
        productData.tax_status || 'taxable', // Tax status
        productData.tax_class || '', // Tax class
        attributeData.attr1_name, // Attribute 1 name
        `"${attributeData.attr1_values}"`, // Attribute 1 values
        attributeData.attr1_visible, // Attribute 1 visible
        attributeData.attr1_global, // Attribute 1 global
        attributeData.attr2_name, // Attribute 2 name
        `"${attributeData.attr2_values}"`, // Attribute 2 values
        attributeData.attr2_visible, // Attribute 2 visible
        attributeData.attr2_global, // Attribute 2 global
        attributeData.attr3_name, // Attribute 3 name
        `"${attributeData.attr3_values}"`, // Attribute 3 values
        attributeData.attr3_visible, // Attribute 3 visible
        attributeData.attr3_global, // Attribute 3 global
        attributeData.attr4_name, // Attribute 4 name
        `"${attributeData.attr4_values}"`, // Attribute 4 values
        attributeData.attr4_visible, // Attribute 4 visible
        attributeData.attr4_global, // Attribute 4 global
        attributeData.attr5_name, // Attribute 5 name
        `"${attributeData.attr5_values}"`, // Attribute 5 values
        attributeData.attr5_visible, // Attribute 5 visible
        attributeData.attr5_global, // Attribute 5 global
        '' // Parent (empty for parent products)
      ];
      
      // Write parent product row
      fs.appendFileSync(OUTPUT_FILE, parentRow.join(',') + '\n');
      
      // Get variations if it's a variable product
      if (productData.type === 'variable') {
        console.log(`Fetching variations for product ${product.id}...`);
        
        try {
          const variationsResponse = await api.get(`products/${product.id}/variations`, {
            per_page: 100
          });
          
          console.log(`Found ${variationsResponse.data.length} variations`);
          
          // Process each variation
          for (const variation of variationsResponse.data) {
            const variationAttributes = {};
            
            // Extract variation attributes
            for (let i = 0; i < 5; i++) {
              const attr = attributes[i];
              if (attr) {
                const variationAttr = variation.attributes.find(va => va.name === attr.name);
                variationAttributes[`attr${i+1}_name`] = attr.name;
                variationAttributes[`attr${i+1}_values`] = variationAttr ? variationAttr.option : '';
                variationAttributes[`attr${i+1}_visible`] = attr.visible ? 1 : 0;
                variationAttributes[`attr${i+1}_global`] = attr.variation ? 0 : 1;
              } else {
                variationAttributes[`attr${i+1}_name`] = '';
                variationAttributes[`attr${i+1}_values`] = '';
                variationAttributes[`attr${i+1}_visible`] = '';
                variationAttributes[`attr${i+1}_global`] = '';
              }
            }
            
            // Create variation row
            const variationRow = [
              'variation', // Type
              variation.sku || '', // SKU
              `"${productData.name} - ${variation.attributes.map(attr => `${attr.name} ${attr.option}`).join(' ')}"`, // Name
              variation.status === 'publish' ? 1 : 0, // Published
              0, // Is featured (variations are not featured)
              '', // Short description
              '', // Description
              variation.regular_price || '', // Regular price
              variation.sale_price || '', // Sale price
              '', // Categories (inherited from parent)
              '', // Tags (inherited from parent)
              variation.image ? variation.image.src : '', // Images
              variation.stock_quantity || '', // Stock
              variation.in_stock ? 1 : 0, // In stock
              variation.weight || '', // Weight
              variation.dimensions.length || '', // Length
              variation.dimensions.width || '', // Width
              variation.dimensions.height || '', // Height
              '', // Shipping class (inherited from parent)
              '', // Tax status (inherited from parent)
              '', // Tax class (inherited from parent)
              variationAttributes.attr1_name, // Attribute 1 name
              variationAttributes.attr1_values, // Attribute 1 values
              variationAttributes.attr1_visible, // Attribute 1 visible
              variationAttributes.attr1_global, // Attribute 1 global
              variationAttributes.attr2_name, // Attribute 2 name
              variationAttributes.attr2_values, // Attribute 2 values
              variationAttributes.attr2_visible, // Attribute 2 visible
              variationAttributes.attr2_global, // Attribute 2 global
              variationAttributes.attr3_name, // Attribute 3 name
              variationAttributes.attr3_values, // Attribute 3 values
              variationAttributes.attr3_visible, // Attribute 3 visible
              variationAttributes.attr3_global, // Attribute 3 global
              variationAttributes.attr4_name, // Attribute 4 name
              variationAttributes.attr4_values, // Attribute 4 values
              variationAttributes.attr4_visible, // Attribute 4 visible
              variationAttributes.attr4_global, // Attribute 4 global
              variationAttributes.attr5_name, // Attribute 5 name
              variationAttributes.attr5_values, // Attribute 5 values
              variationAttributes.attr5_visible, // Attribute 5 visible
              variationAttributes.attr5_global, // Attribute 5 global
              productData.sku || productData.id // Parent SKU
            ];
            
            // Write variation row
            fs.appendFileSync(OUTPUT_FILE, variationRow.join(',') + '\n');
          }
        } catch (error) {
          console.error(`Error fetching variations for product ${product.id}: ${error.message}`);
        }
      }
      
      // Be nice to the API
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`Air Optix Colors data saved to ${OUTPUT_FILE}`);
    
  } catch (error) {
    console.error(`Error downloading Air Optix Colors data: ${error.message}`);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the function
downloadAirOptixColors();
