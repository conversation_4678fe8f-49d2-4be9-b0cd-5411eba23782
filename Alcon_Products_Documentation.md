# Alcon Contact Lens Products - CSV Import Templates

## Overview
This package contains CSV import templates for 4 Alcon contact lens products with complex variable structures including multiple prescription parameters.

## Product Files Created

### 1. `alcon_air_optix_astigmatizam.csv`
**Air Optix za astigmatizam** - Monthly contact lenses for astigmatism
- **Attributes**: SPH, CYL, AXIS, Brand, Pieces
- **SPH Range**: +6.00 to -6.00 (0.25 steps), -6.50 to -10.00 (0.50 steps)
- **CYL Values**: -0.75, -1.25, -1.75, -2.25
- **AXIS Range**: 10° to 180° (10° increments)
- **Total Variations**: 4,284 possible combinations
- **Sample included**: First 18 variations (SPH +6.00, CYL -0.75, all AXIS values)

### 2. `alcon_air_optix_multifocal.csv`
**Air Optix Aqua Multifocal** - Monthly multifocal contact lenses
- **Attributes**: SPH, ADD, <PERSON>, Pieces
- **SPH Range**: +6.00 to -10.00 (0.25 steps)
- **ADD Values**: LOW (up to +1.25), MEDIUM (+1.50 to +2.00), HIGH (+2.25 to +2.50)
- **Total Variations**: 195 combinations (65 SPH × 3 ADD)
- **Sample included**: First 21 variations

### 3. `alcon_dailies_toric.csv`
**DAILIES Aqua Comfort PLUS Toric** - Daily contact lenses for astigmatism
- **Attributes**: SPH, CYL, AXIS, Brand, Pieces
- **SPH Range**: +4.00 to -6.00 (0.25 steps), -6.50 to -8.00 (0.50 steps)
- **CYL Values**: -0.75, -1.25, -1.75
- **AXIS Values**: 10°, 20°, 70°, 80°, 90°, 100°, 110°, 160°, 170°, 180°
- **Total Variations**: 1,470 combinations
- **Sample included**: First 21 variations

### 4. `alcon_dailies_multifocal.csv`
**DAILIES Aqua Comfort PLUS Multifocal** - Daily multifocal contact lenses
- **Attributes**: SPH, ADD, Brand, Pieces
- **SPH Range**: +6.00 to -10.00 (0.25 steps)
- **ADD Values**: LOW, MEDIUM, HIGH
- **Total Variations**: 195 combinations
- **Sample included**: First 21 variations

## Important Notes

### File Size Limitations
Due to the massive number of possible variations (especially for toric lenses), the CSV files contain **sample variations only**. Each file includes:
- 1 parent product row
- 20+ sample variation rows to demonstrate the structure

### Complete Implementation
To create complete product catalogs, you would need to:

1. **Generate all combinations** using the ranges specified above
2. **Split into multiple CSV files** (WooCommerce recommends max 100-500 products per import)
3. **Import in batches** to avoid server timeouts

## Attribute Explanations

### SPH (Spherical Power)
- Corrects nearsightedness (-) or farsightedness (+)
- Measured in diopters
- Different step increments for different ranges

### CYL (Cylinder)
- Corrects astigmatism
- Always negative values
- Limited to specific values per product

### AXIS
- Direction of astigmatism correction
- Measured in degrees (0° to 180°)
- Different available values per product

### ADD (Addition)
- Additional power for near vision in multifocal lenses
- LOW: up to +1.25
- MEDIUM: +1.50 to +2.00  
- HIGH: +2.25 to +2.50

## Customization Guide

### Step 1: Update Product Information
For each CSV file, modify the parent product row:
- **Name**: Update product name
- **Description**: Add detailed product description
- **Price**: Set your pricing
- **Images**: Add product image URLs
- **Categories**: Use your store's category structure

### Step 2: Generate Complete Variations
Use the sample structure to generate all required combinations:

```
For Air Optix Astigmatizam:
- SPH: +6.00 to -6.00 (0.25), -6.50 to -10.00 (0.50) = 57 values
- CYL: 4 values
- AXIS: 18 values  
- Total: 57 × 4 × 18 = 4,104 variations
```

### Step 3: Batch Import Strategy
1. **Split by SPH ranges** (e.g., +6.00 to +3.00, +2.75 to 0.00, etc.)
2. **Import 100-200 variations per batch**
3. **Test with small batches first**

## SKU Naming Convention

The templates use a systematic SKU naming:
- **Parent**: `PRODUCT-NAME-MAIN`
- **Variations**: `PRODUCT-NAME-SPH-CYL-AXIS` or `PRODUCT-NAME-SPH-ADD`

Examples:
- `AIR-OPTIX-ASTIG-P600-075-10` = SPH +6.00, CYL -0.75, AXIS 10°
- `DAILIES-MULTI-P575-MEDIUM` = SPH +5.75, ADD MEDIUM

## Import Process

1. **Prepare your CSV** with complete variations
2. **Go to WooCommerce → Products → Import**
3. **Upload CSV file**
4. **Map columns** (should auto-map with these templates)
5. **Run import**
6. **Verify results** before importing next batch

## Performance Considerations

### Server Requirements
- **Memory**: Increase PHP memory limit (512MB+)
- **Execution Time**: Increase max_execution_time (300s+)
- **Upload Size**: Increase max file upload size

### Optimization Tips
- Import during low-traffic hours
- Use staging environment for testing
- Monitor server resources during import
- Consider using WooCommerce Product CSV Import Suite for large imports

## Troubleshooting

### Common Issues
1. **Timeout errors**: Reduce batch size
2. **Memory errors**: Increase PHP memory limit
3. **Duplicate SKUs**: Ensure all variation SKUs are unique
4. **Missing variations**: Check Parent column matches exactly

### Validation Checklist
- [ ] All variation SKUs are unique
- [ ] Parent column matches parent product SKU
- [ ] Attribute values are consistent
- [ ] Prices are set for all variations
- [ ] Required fields are populated

## Next Steps

1. **Choose which products to implement first**
2. **Generate complete variation sets**
3. **Test import with small batches**
4. **Scale up to full product catalogs**
5. **Set up automated inventory management**

These templates provide a solid foundation for importing complex contact lens products with multiple prescription parameters into WooCommerce.
