# Contact Lens Variable Product CSV Import Template

## Overview
This CSV template is designed for importing contact lens variable products into WooCommerce, based on the structure observed on the Diopta website (https://diopta.rs/kontaktna-sociva/).

## Template Structure

### Variable Product Components
1. **Parent Product** (Row 1): The main variable product containing all possible variations
2. **Variation Products** (Rows 2-53): Individual variations for each SPH (Spherical Power) value

### Key Attributes
The template includes 5 attributes that are common for contact lenses:

1. **SPH (Spherical Power)** - Primary variable attribute
   - Range: -10.00 to +5.00 in 0.25 increments
   - This is what customers select when choosing their prescription

2. **Brand** - Manufacturer (e.g., Alcon, Bausch & Lomb, Coopervision)
3. **Base Curve** - Curvature of the lens (e.g., 8.5, 8.6)
4. **Wear Time** - Duration of use (Daily, Monthly)
5. **Pieces** - Number of lenses in package (3, 30, etc.)

## How to Use This Template

### Step 1: Customize the Parent Product
Edit the first row with your product information:
- **SKU**: Change `EXAMPLE-LENS-MAIN` to your product's main SKU
- **Name**: Change `Example Contact Lens` to your product name
- **Description**: Update with your product details
- **Price**: Set your regular price (same for all variations)
- **Categories**: Use `Kontaktna sočiva` or your category name
- **Images**: Add your product image URL
- **Attributes**: Update Brand, Base Curve, Wear Time, and Pieces values

### Step 2: Customize Variations
For each variation row (rows 2-53):
- **SKU**: Update the variation SKUs (keep them unique)
- **Name**: Update variation names to include your product name
- **Parent**: Change `EXAMPLE-LENS-MAIN` to match your parent product SKU
- **Attributes**: Update Brand, Base Curve, Wear Time, and Pieces to match parent

### Step 3: Adjust SPH Range (Optional)
If your product doesn't need the full SPH range:
- Remove unnecessary variation rows
- Update the parent product's "Attribute 1 value(s)" column to only include the SPH values you're keeping

### Step 4: Import Process
1. Go to WooCommerce → Products → Import
2. Upload your customized CSV file
3. Map the columns (should auto-map if using this template)
4. Run the import

## Column Explanations

### Required Columns
- **Type**: `variable` for parent, `variation` for variations
- **SKU**: Unique identifier for each product/variation
- **Name**: Product/variation name
- **Published**: 1 = published, 0 = private, -1 = draft
- **Regular price**: Price in your store currency
- **Parent**: SKU of parent product (for variations only)

### Attribute Columns (Pattern repeats for each attribute)
- **Attribute X name**: Name of the attribute (e.g., "SPH", "Brand")
- **Attribute X value(s)**: 
  - For parent: All possible values (comma-separated)
  - For variations: Single value for this variation
- **Attribute X visible**: 1 = visible on product page, 0 = hidden
- **Attribute X global**: 1 = global attribute, 0 = product-specific

### Optional Columns
- **Short description**: Brief product description
- **Description**: Full product description
- **Categories**: Product categories (use existing category names)
- **Tags**: Product tags
- **Images**: Image URLs (first image becomes featured image)
- **Stock**: Stock quantity
- **Weight/Dimensions**: Physical product properties

## Tips for Success

1. **Test with a small batch first**: Import just a few variations to test the process
2. **Use consistent naming**: Keep SKU and naming patterns consistent
3. **Check existing categories**: Use category names that already exist in your store
4. **Validate URLs**: Ensure image URLs are accessible
5. **Backup first**: Always backup your store before importing

## Common Issues and Solutions

### Issue: Variations not appearing
**Solution**: Ensure the Parent column in variations matches the parent product's SKU exactly

### Issue: Attributes not working
**Solution**: Check that attribute names are consistent between parent and variations

### Issue: Images not importing
**Solution**: Verify image URLs are direct links to image files, not redirects

### Issue: Categories not assigned
**Solution**: Use exact category names that exist in your WooCommerce store

## Example Customization

To create a "DAILIES TOTAL1" product like on Diopta:

1. Change parent SKU to: `DAILIES-TOTAL1-MAIN`
2. Change product name to: `DAILIES TOTAL1`
3. Update brand to: `Alcon`
4. Set base curve to: `8.5`
5. Set wear time to: `Daily`
6. Set pieces to: `30`
7. Update all variation SKUs and parent references accordingly

## Support

For WooCommerce-specific import issues, refer to:
- [WooCommerce CSV Import Documentation](https://woocommerce.com/document/product-csv-importer-exporter/)
- WooCommerce support forums

This template follows WooCommerce's official CSV import schema and should work with any standard WooCommerce installation.
