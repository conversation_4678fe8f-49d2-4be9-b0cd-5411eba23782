import csv

# Input and output file paths
input_csv = 'extracted_data.csv'
output_csv = 'unique_grp.csv'

# Use a set to store unique 'grp' values
unique_grp = set()

# Read the input CSV file and extract unique 'grp' values
with open(input_csv, 'r') as csvfile:
    reader = csv.DictReader(csvfile)
    for row in reader:
        unique_grp.add(row['grp'])

# Write the unique 'grp' values to a new CSV file
with open(output_csv, 'w', newline='') as csvfile:
    fieldnames = ['grp']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

    # Write the header row
    writer.writeheader()

    # Write each unique 'grp' value to the CSV
    for grp in sorted(unique_grp):  # Sort the values for better readability
        writer.writerow({'grp': grp})

print(f"Unique 'grp' extraction complete. Check '{output_csv}'.")