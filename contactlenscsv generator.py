import csv
import io

def generate_sph_range(start, end, step):
    """Generate SPH values in the given range with specified step"""
    values = []
    current = start
    if step > 0:
        while current <= end:
            values.append(f"{current:+.2f}" if current != 0 else "0.00")
            current += step
    else:
        while current >= end:
            values.append(f"{current:+.2f}" if current != 0 else "0.00")
            current += step
    return values

def generate_ax_range(start, end, step):
    """Generate AX values from start to end with specified step"""
    values = []
    current = start
    while current <= end:
        values.append(f"{current}°")
        current += step
    return values

def create_variations():
    """Create all variations based on the specifications"""
    variations = []
    variation_id = 285126  # Starting ID from the example
    
    # Group 1: SPH +4.00 to +0.25 (0.25 steps)
    sph_positive = generate_sph_range(0.25, 4.00, 0.25)
    
    # CYL:-2.25, AX: 10°, 20°, 160°, 170°, 180°
    cyl_2_25_ax = ["10°", "20°", "160°", "170°", "180°"]
    for sph in sph_positive:
        for ax in cyl_2_25_ax:
            variations.append({
                'id': variation_id,
                'sph': sph,
                'cyl': '-2.25',
                'ax': ax
            })
            variation_id += 1
    
    # CYL:-0.75, -1.25, -1.75, AX: 10°, 20°, 70°, 80°, 90°, 100°, 110°, 160°, 170°, 180°
    cyl_others = ['-0.75', '-1.25', '-1.75']
    ax_full = ["10°", "20°", "70°", "80°", "90°", "100°", "110°", "160°", "170°", "180°"]
    for sph in sph_positive:
        for cyl in cyl_others:
            for ax in ax_full:
                variations.append({
                    'id': variation_id,
                    'sph': sph,
                    'cyl': cyl,
                    'ax': ax
                })
                variation_id += 1
    
    # Group 2: SPH 0.00 to -6.00 (0.25 steps)
    sph_negative = ["0.00"] + generate_sph_range(-0.25, -6.00, -0.25)
    
    # CYL:-0.75, -1.25, -1.75, AX: 10° to 180° in 10° steps
    ax_all_steps = generate_ax_range(10, 180, 10)
    for sph in sph_negative:
        for cyl in cyl_others:
            for ax in ax_all_steps:
                variations.append({
                    'id': variation_id,
                    'sph': sph,
                    'cyl': cyl,
                    'ax': ax
                })
                variation_id += 1
    
    # CYL:-2.25, AX: 10°, 20°, 70°, 80°, 90°, 100°, 110°, 160°, 170°, 180°
    for sph in sph_negative:
        for ax in ax_full:
            variations.append({
                'id': variation_id,
                'sph': sph,
                'cyl': '-2.25',
                'ax': ax
            })
            variation_id += 1
    
    # Group 3: SPH -6.50 to -8.00 (0.50 steps)
    sph_high_negative = generate_sph_range(-6.50, -8.00, -0.50)
    
    # CYL:-0.75, -1.25, -1.75, AX: 10°, 20°, 70°, 80°, 90°, 100°, 110°, 160°, 170°, 180°
    for sph in sph_high_negative:
        for cyl in cyl_others:
            for ax in ax_full:
                variations.append({
                    'id': variation_id,
                    'sph': sph,
                    'cyl': cyl,
                    'ax': ax
                })
                variation_id += 1
    
    # CYL:-2.25, AX: 10°, 20°, 160°, 170°, 180°
    for sph in sph_high_negative:
        for ax in cyl_2_25_ax:
            variations.append({
                'id': variation_id,
                'sph': sph,
                'cyl': '-2.25',
                'ax': ax
            })
            variation_id += 1
    
    return variations

def create_csv_content():
    """Create the complete CSV content with all variations"""
    variations = create_variations()
    
    # Simplified CSV header matching your format
    header = [
        "ID", "Type", "Name", "Published", "Is featured?", "Visibility in catalog", 
        "Short description", "Description", "In stock?", "Backorders allowed?", 
        "Regular price", "Categories", "Images", "Parent", "Attribute 1 name", 
        "Attribute 1 value(s)", "Attribute 1 visible", "Attribute 1 global", 
        "Attribute 2 name", "Attribute 2 value(s)", "Attribute 2 visible", 
        "Attribute 2 global", "Attribute 3 name", "Attribute 3 value(s)", 
        "Attribute 3 visible", "Attribute 3 global", "Attribute 4 name", 
        "Attribute 4 value(s)", "Attribute 4 visible", "Attribute 4 global", 
        "Attribute 5 name", "Attribute 5 value(s)", "Attribute 5 visible", 
        "Attribute 5 global", "Attribute 6 name", "Attribute 6 value(s)", 
        "Attribute 6 visible", "Attribute 6 global", "Meta: avansno_placanje", 
        "Meta: _avansno_placanje"
    ]
    
    # Create CSV content
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(header)
    
    # Get all unique SPH values used in variations (sorted)
    all_sph_values = sorted(set([var['sph'] for var in variations]), key=float)
    sph_values_string = ", ".join(all_sph_values)
    
    # Write the main variable product (parent)
    main_product_row = [
        285125,  # ID
        'variable',  # Type
        'TOTAL30 ZA ASTIGMATIZAM',  # Name
        -1,  # Published
        0,   # Is featured?
        'visible',  # Visibility in catalog
        'Bazna krivina: 8.6\\nVreme nošenja: Mesečna\\nBroj komada: 6\\nTip: Transparentna\\nProizvođač: Alcon',  # Short description
        '<div style=""line-height: 1.6; max-width: 600px;""><strong style=""color: #f38120; font-size: 18px;"">SPECIFIKACIJE PROIZVODA</strong>\\n\\n<strong>Materijal:</strong> Lenfilcon A\\n<strong>Sadržaj vode:</strong> U jezgru 55%, na površini 100%\\n<strong>Dk/t:</strong> 123 @ -3.00 D (-1.25 x 180°)\\n<strong>Prečnik:</strong> 14.5 mm\\n<strong>Bazna krivina:</strong> 8.6 mm\\n<strong>Debljina u centru:</strong> 0.10 mm\\n\\n<strong>Opseg dioptrija (korak):</strong>\\n<ul style=""margin-top: 0;"">\\n <li>***** do -6.00 (korak 0.25 D)</li>\\n <li>***** do ***** i -6.50 do -10.00 (korak 0.50 D)</li>\\n <li>Cyl: -0.75 do -2.25 (korak 0.50 D)</li>\\n <li>Axis: 10° do 180° (korak 10°)</li>\\n</ul>\\n<strong>Boja za lakše rukovanje:</strong> Svetlo zelena, VISITINT®\\n<strong>Pakovanje:</strong> 6 sočiva u kutiji\\n<strong>Režim nošenja / zamene:</strong> Dnevno nošenje / Mesečna zamena\\n<strong>Dizajn:</strong> CELLIGENT® tehnologija, gradient vode, UV filter klase I (apsorpcija), HEVL filter\\n\\n</div>',  # Description
        1,   # In stock?
        1,   # Backorders allowed?
        '',  # Regular price (empty for variable product)
        'Kontaktna sočiva',  # Categories
        'https://diopta.online/total30-ast.webp',  # Images
        '',  # Parent (empty for main product)
        'Brend sociva',  # Attribute 1 name
        'Alcon',  # Attribute 1 value(s)
        1,   # Attribute 1 visible
        1,   # Attribute 1 global
        'TIp sočiva',  # Attribute 2 name
        'Transparentna',  # Attribute 2 value(s)
        1,   # Attribute 2 visible
        1,   # Attribute 2 global
        'Vreme nošenja',  # Attribute 3 name
        'Mesečna',  # Attribute 3 value(s)
        1,   # Attribute 3 visible
        1,   # Attribute 3 global
        'SPH',  # Attribute 4 name
        sph_values_string,  # Attribute 4 value(s) - all SPH values
        1,   # Attribute 4 visible
        1,   # Attribute 4 global
        'CYL',  # Attribute 5 name
        '-0.75, -1.25, -1.75, -2.25',  # Attribute 5 value(s)
        1,   # Attribute 5 visible
        1,   # Attribute 5 global
        'AX',  # Attribute 6 name
        '10°, 20°, 30°, 40°, 50°, 60°, 70°, 80°, 90°, 100°, 110°, 120°, 130°, 140°, 150°, 160°, 170°, 180°',  # Attribute 6 value(s)
        1,   # Attribute 6 visible
        1,   # Attribute 6 global
        1,   # Meta: avansno_placanje
        'field_684997befb16d'  # Meta: _avansno_placanje
    ]
    
    writer.writerow(main_product_row)
    
    # Write each variation
    for variation in variations:
        variation_row = [
            variation['id'],  # ID
            'variation',      # Type
            'TOTAL30 ZA ASTIGMATIZAM',  # Name
            -1,               # Published
            0,                # Is featured?
            'visible',        # Visibility in catalog
            '',               # Short description
            '',               # Description
            'backorder',      # In stock?
            1,                # Backorders allowed?
            4810,             # Regular price
            '',               # Categories
            '',               # Images
            'id:285125',      # Parent
            '',               # Attribute 1 name
            '',               # Attribute 1 value(s)
            1,                # Attribute 1 visible
            1,                # Attribute 1 global
            '',               # Attribute 2 name
            '',               # Attribute 2 value(s)
            1,                # Attribute 2 visible
            1,                # Attribute 2 global
            '',               # Attribute 3 name
            '',               # Attribute 3 value(s)
            1,                # Attribute 3 visible
            1,                # Attribute 3 global
            'SPH',            # Attribute 4 name
            variation['sph'], # Attribute 4 value(s)
            '',               # Attribute 4 visible
            '',               # Attribute 4 global
            'CYL',            # Attribute 5 name
            variation['cyl'], # Attribute 5 value(s)
            '',               # Attribute 5 visible
            '',               # Attribute 5 global
            'AX',             # Attribute 6 name
            variation['ax'],  # Attribute 6 value(s)
            '',               # Attribute 6 visible
            '',               # Attribute 6 global
            '',               # Meta: avansno_placanje
            ''                # Meta: _avansno_placanje
        ]
        
        writer.writerow(variation_row)
    
    return output.getvalue()

# Generate the CSV content
csv_content = create_csv_content()

# Print detailed summary with actual values used
variations = create_variations()
print(f"Generated {len(variations)} variations")

# Get unique values for each attribute that are actually used in variations
unique_sph = sorted(set([var['sph'] for var in variations]), key=float)
unique_cyl = sorted(set([var['cyl'] for var in variations]), key=float)
unique_ax = sorted(set([var['ax'] for var in variations]), key=lambda x: int(x.replace('°', '')))

print(f"\nATTRIBUTE VALUES THAT WILL BE USED IN VARIATIONS:")
print(f"SPH values ({len(unique_sph)}): {', '.join(unique_sph)}")
print(f"CYL values ({len(unique_cyl)}): {', '.join(unique_cyl)}")
print(f"AX values ({len(unique_ax)}): {', '.join(unique_ax)}")

# Count by SPH groups
positive_count = len([v for v in variations if v['sph'].startswith('+')])
zero_negative_count = len([v for v in variations if v['sph'] == '0.00' or (v['sph'].startswith('-') and float(v['sph']) >= -6.00)])
high_negative_count = len([v for v in variations if v['sph'].startswith('-') and float(v['sph']) < -6.00])

print(f"\nBREAKDOWN BY SPH GROUPS:")
print(f"Positive SPH (+0.25 to +4.00): {positive_count} variations")
print(f"Zero to -6.00 SPH: {zero_negative_count} variations") 
print(f"High negative SPH (-6.50 to -8.00): {high_negative_count} variations")

print("\nFirst few variations:")
for i, var in enumerate(variations[:5]):
    print(f"ID: {var['id']}, SPH: {var['sph']}, CYL: {var['cyl']}, AX: {var['ax']}")

print(f"\nLast few variations:")
for var in variations[-5:]:
    print(f"ID: {var['id']}, SPH: {var['sph']}, CYL: {var['cyl']}, AX: {var['ax']}")

print(f"\nVARIATION COMBINATIONS SUMMARY:")
print(f"- SPH +0.25 to +4.00 with CYL -2.25 and limited AX values: {len([v for v in variations if v['sph'].startswith('+') and v['cyl'] == '-2.25'])} variations")
print(f"- SPH +0.25 to +4.00 with CYL -0.75/-1.25/-1.75 and full AX values: {len([v for v in variations if v['sph'].startswith('+') and v['cyl'] != '-2.25'])} variations")
print(f"- SPH 0.00 to -6.00 with CYL -0.75/-1.25/-1.75 and all AX values: {len([v for v in variations if (v['sph'] == '0.00' or (v['sph'].startswith('-') and float(v['sph']) >= -6.00)) and v['cyl'] != '-2.25'])} variations")
print(f"- SPH 0.00 to -6.00 with CYL -2.25 and limited AX values: {len([v for v in variations if (v['sph'] == '0.00' or (v['sph'].startswith('-') and float(v['sph']) >= -6.00)) and v['cyl'] == '-2.25'])} variations")
print(f"- SPH -6.50 to -8.00 with all CYL/AX combinations: {len([v for v in variations if v['sph'].startswith('-') and float(v['sph']) < -6.00])} variations")

# Save to file
with open('total30_astigmatism_variations.csv', 'w', newline='', encoding='utf-8') as file:
    file.write(csv_content)

print(f"\nCSV file 'total30_astigmatism_variations.csv' created successfully!")
print(f"Total rows: 1 main product + {len(variations)} variations = {len(variations) + 1} rows")